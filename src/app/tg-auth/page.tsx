"use client";

import TelegramAuth from "@/components/telegram-auth";
import { useDidMount } from "@/hooks/useDidMount";
import { initData, useSignal } from "@telegram-apps/sdk-react";

function InitDataDebug() {
  const initDataUser = useSignal(initData.user);
  const initDataRaw = useSignal(initData.raw);

  return (
    <div className="mb-8 p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">InitData Debug</h3>
      <div className="space-y-2">
        <div>
          <strong>initData.user:</strong>{" "}
          <span className="font-mono text-sm">
            {initDataUser ? JSON.stringify(initDataUser, null, 2) : "undefined"}
          </span>
        </div>
        <div>
          <strong>initData.raw:</strong>{" "}
          <span className="font-mono text-sm">
            {initDataRaw ? initDataRaw : "undefined"}
          </span>
        </div>
      </div>
    </div>
  );
}

export default function TelegramAuthPage() {
  const didMount = useDidMount();

  if (!didMount) {
    return null;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <InitDataDebug />

        <div className="mb-12 p-6 border rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50">
          <TelegramAuth
            onSuccess={() => {
              window.location.href = "/profile";
            }}
            onError={(error) => {
              alert(`Authentication failed: ${error}`);
            }}
          />
        </div>

        <div className="mb-8">{/* <TelegramTest /> */}</div>
      </div>
    </div>
  );
}
