"use client";

import { useEffect, useState } from "react";
import { useSignal, initData, miniApp } from "@telegram-apps/sdk-react";

export const TelegramDebug = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Try to use Telegram hooks
  const isDark = useSignal(miniApp.isDark);
  const initDataUser = useSignal(initData.user);
  const initDataRaw = useSignal(initData.raw);

  if (!isClient) {
    return (
      <div className="p-6 border rounded-lg bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Telegram Debug</h3>
        <p className="text-gray-600">Loading...</p>
      </div>
    );
  }

  return (
    <div className="p-6 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Telegram Debug</h3>

      {/* Environment Info */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800 font-medium text-sm">
          🔧 Environment: {process.env.NODE_ENV}
        </p>
        <p className="text-blue-800 text-sm">
          🌐 Client: {typeof window !== "undefined" ? "Yes" : "No"}
        </p>
      </div>

      {/* Init Data */}
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">Init Data:</h4>
        <div className="p-2 bg-gray-100 rounded text-sm">
          <pre className="whitespace-pre-wrap overflow-auto">
            {JSON.stringify(
              {
                user: initDataUser || "Not available",
                raw: initDataRaw || "Not available",
                isDark: isDark,
              },
              null,
              2
            )}
          </pre>
        </div>
      </div>

      {/* Window Telegram Object */}
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">Window.Telegram:</h4>
        <div className="p-2 bg-gray-100 rounded text-sm">
          <pre className="whitespace-pre-wrap overflow-auto">
            {typeof window !== "undefined" && window.Telegram
              ? JSON.stringify(
                  {
                    WebApp: {
                      initData: window.Telegram.WebApp?.initData || "Empty",
                      initDataUnsafe:
                        window.Telegram.WebApp?.initDataUnsafe || "Empty",
                      platform: window.Telegram.WebApp?.platform || "Unknown",
                      version: window.Telegram.WebApp?.version || "Unknown",
                    },
                  },
                  null,
                  2
                )
              : "Not available"}
          </pre>
        </div>
      </div>
    </div>
  );
};
