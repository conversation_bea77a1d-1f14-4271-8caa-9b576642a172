"use client";

import { type PropsWithChildren, useEffect, useState } from "react";
import { retrieveLaunchParams } from "@telegram-apps/sdk-react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";
import { useDidMount } from "@/hooks/useDidMount";
import { init } from "@/core/init";
import { mockEnv } from "@/utils/mock-env";

function RootInner({ children }: PropsWithChildren) {
  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  const [isSDKInitialized, setIsSDKInitialized] = useState(false);
  const didMount = useDidMount();

  useEffect(() => {
    if (!didMount) return;

    const initializeSDK = async () => {
      try {
        // First mock the environment if needed
        await mockEnv();

        // Then retrieve launch params and initialize
        const launchParams = retrieveLaunchParams();
        const { tgWebAppPlatform: platform } = launchParams;
        const debug =
          (launchParams.tgWebAppStartParam ?? "").includes("debug") ||
          process.env.NODE_ENV === "development";

        // Configure all application dependencies.
        await init({
          debug,
          eruda: debug && ["ios", "android"].includes(platform),
          mockForMacOS: platform === "macos",
        });

        setIsSDKInitialized(true);
      } catch (e) {
        console.error("Failed to initialize Telegram SDK:", e);
        // Still set as initialized to prevent infinite loading
        setIsSDKInitialized(true);
      }
    };

    initializeSDK();
  }, [didMount]);

  if (!didMount || !isSDKInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">
            {!didMount ? "Loading..." : "Initializing Telegram SDK..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}
