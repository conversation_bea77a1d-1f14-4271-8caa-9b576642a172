"use client";

import { type PropsWith<PERSON>hildren, useEffect, useState } from "react";
import { init as initSDK, restoreInitData } from "@telegram-apps/sdk-react";

import { TelegramErrorBoundary } from "@/components/TelegramErrorBoundary";
import { useDidMount } from "@/hooks/useDidMount";
import { mockEnv } from "@/utils/mock-env";

function RootInner({ children }: PropsWithChildren) {
  return <>{children}</>;
}

export function Root(props: PropsWithChildren) {
  const [isSDKInitialized, setIsSDKInitialized] = useState(false);
  const didMount = useDidMount();

  useEffect(() => {
    if (!didMount) return;

    const initializeSDK = async () => {
      try {
        console.log("[Root] Starting SDK initialization...");

        // First mock the environment if needed
        await mockEnv();
        console.log("[Root] Mock environment setup complete");

        // Initialize the SDK first
        initSDK();
        console.log("[Root] SDK initialized");

        // Restore initData component to make it accessible
        restoreInitData();
        console.log("[Root] InitData component restored");

        setIsSDKInitialized(true);
        console.log("[Root] SDK initialization complete");
      } catch (e) {
        console.error("[Root] Failed to initialize Telegram SDK:", e);
        // Still set as initialized to prevent infinite loading
        setIsSDKInitialized(true);
      }
    };

    initializeSDK();
  }, [didMount]);

  if (!didMount || !isSDKInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">
            {!didMount ? "Loading..." : "Initializing Telegram SDK..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <TelegramErrorBoundary>
      <RootInner {...props} />
    </TelegramErrorBoundary>
  );
}
